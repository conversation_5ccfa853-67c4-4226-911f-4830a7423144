fileFormatVersion: 2
guid: d936e851d3e9e61499c68d921eac9414
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_0
      rect:
        serializedVersion: 2
        x: 0
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 002496318ec40de49931294a6d2e67cc
      internalID: -929040053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_1
      rect:
        serializedVersion: 2
        x: 200
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 04ef2affdc2ca7c4dadc9dd611bdceea
      internalID: 68518181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_2
      rect:
        serializedVersion: 2
        x: 400
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 837f91de048ae7843a2bf169818c1d2a
      internalID: -761472057
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_3
      rect:
        serializedVersion: 2
        x: 600
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8bc120bbdf5c2ba47833242bb9a9cbdd
      internalID: -526582603
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cdbeaf7eb5f789a46b44fd1919ae7853
      internalID: -936059128
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_5
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1ddfe1690d672cb47a519c3209b14b89
      internalID: 637945446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_6
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fb12c6dfbb37900488d60f115c2e25de
      internalID: -724680923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_7
      rect:
        serializedVersion: 2
        x: 600
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6098ec87b973b0f488972799d8661134
      internalID: -1352468472
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_0: -929040053
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_1: 68518181
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_2: -761472057
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_3: -526582603
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_4: -936059128
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_5: 637945446
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_6: -724680923
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_301_7: -1352468472
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
