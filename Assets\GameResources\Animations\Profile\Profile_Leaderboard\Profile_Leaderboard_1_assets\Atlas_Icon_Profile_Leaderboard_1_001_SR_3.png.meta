fileFormatVersion: 2
guid: a933eca74d0497142827018fd42f32f4
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_0
      rect:
        serializedVersion: 2
        x: 0
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0e9caeef499878d4b87f45ec341ddc14
      internalID: -362099744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_1
      rect:
        serializedVersion: 2
        x: 200
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: be9ebd05ee367df4fb2aef83e7b672d9
      internalID: -1767743628
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_2
      rect:
        serializedVersion: 2
        x: 400
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0e1f62e21dd75f94fade222bace57599
      internalID: 79779168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_3
      rect:
        serializedVersion: 2
        x: 600
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 80fbfd751ac68b043a5ecdb152ae131f
      internalID: 1342495547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_4
      rect:
        serializedVersion: 2
        x: 0
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ecf20f4295a73e441b00b75c3e4b3d04
      internalID: -1212445751
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_5
      rect:
        serializedVersion: 2
        x: 200
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f24b2bdf3c553ca40961f17a592afed1
      internalID: 1379952869
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_6
      rect:
        serializedVersion: 2
        x: 400
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3089f1fd04ab1b447bf2b3a22f77f612
      internalID: 1866485742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_7
      rect:
        serializedVersion: 2
        x: 600
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 94858bc652be33440aec31b48a47120b
      internalID: 540871960
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_8
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1e982a63f4f3c9b4396ad1b0e242ae7e
      internalID: 770372923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_9
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ddb443922f53294e894d32a3e2139ec
      internalID: -1895208433
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_10
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6f43e10ee0a38204e9eaa6df8d5b8ff5
      internalID: -1436699506
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_3_11
      rect:
        serializedVersion: 2
        x: 600
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ecd6456c4c2f97e4f87e6bcc3a9599b7
      internalID: -1496292638
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_0: -362099744
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_1: -1767743628
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_10: -1436699506
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_11: -1496292638
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_2: 79779168
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_3: 1342495547
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_4: -1212445751
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_5: 1379952869
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_6: 1866485742
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_7: 540871960
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_8: 770372923
      Atlas_Icon_Profile_Leaderboard_1_001_SR_3_9: -1895208433
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
