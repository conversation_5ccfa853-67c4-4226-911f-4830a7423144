fileFormatVersion: 2
guid: a2e5bb3b2f67559428962d1e72d05462
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_0
      rect:
        serializedVersion: 2
        x: 0
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0af264dce771ef1429620118a5d080f8
      internalID: 578837534
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_1
      rect:
        serializedVersion: 2
        x: 200
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 29e08264c00b451469c2e62239d22168
      internalID: 1467953867
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_2
      rect:
        serializedVersion: 2
        x: 400
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 122a13c3ec051b74e8658c5267fe301b
      internalID: -1916807165
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_3
      rect:
        serializedVersion: 2
        x: 600
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e262ca6321c54e148ae866e982ce9ba4
      internalID: -1595430652
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_4
      rect:
        serializedVersion: 2
        x: 0
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 82413383f88b7d94f9f35638469f2eb8
      internalID: 550126224
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_5
      rect:
        serializedVersion: 2
        x: 200
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2aec1a9679e0f8041a8785ae6d125000
      internalID: -469179618
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_6
      rect:
        serializedVersion: 2
        x: 400
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 103297fde46e68342b1418d74d7e2b65
      internalID: 1167305861
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_7
      rect:
        serializedVersion: 2
        x: 600
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 07ce5ebe9ee5c684e98b6d2fe65b82ec
      internalID: -931263439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_8
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d12c9d5123d48cb488715a476bace6ec
      internalID: -1134444883
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_9
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6a271e2d8a8a38d4ca1e11e3d4f6fbfd
      internalID: 949450103
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_10
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 749bcd58da04d19428ca679ce2d6507e
      internalID: -823864926
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_5_11
      rect:
        serializedVersion: 2
        x: 600
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1230d62c63dcc443ad33472598bbe41
      internalID: -1231172782
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_0: 578837534
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_1: 1467953867
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_10: -823864926
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_11: -1231172782
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_2: -1916807165
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_3: -1595430652
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_4: 550126224
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_5: -469179618
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_6: 1167305861
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_7: -931263439
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_8: -1134444883
      Atlas_Icon_Profile_Leaderboard_1_001_SR_5_9: 949450103
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
