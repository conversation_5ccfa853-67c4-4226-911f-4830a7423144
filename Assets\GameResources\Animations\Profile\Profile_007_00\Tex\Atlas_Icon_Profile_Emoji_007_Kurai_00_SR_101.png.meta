fileFormatVersion: 2
guid: 310687144d3ee7340b0650de15dcbbc1
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_0
      rect:
        serializedVersion: 2
        x: 0
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7eb27d70c0174414fb064ad0743ef7b4
      internalID: -610628573
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_1
      rect:
        serializedVersion: 2
        x: 200
        y: 199
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 638f252c2663c004eae56ab11e7cb66a
      internalID: -1103683973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_2
      rect:
        serializedVersion: 2
        x: 400
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e8e0c18a9182a0f408e2fa72752b3558
      internalID: -609046576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_3
      rect:
        serializedVersion: 2
        x: 600
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1223ace3f8157bc4f9578f5cf9b0ace8
      internalID: 666538622
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b6f22b16307c7574aab6076d1b410907
      internalID: 1146351855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_5
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 808c6ea63703a1944adfaf3cb6d66dcf
      internalID: -1816087596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_6
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ae9629c02f80184b8afa1c285c289f4
      internalID: -387730858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_7
      rect:
        serializedVersion: 2
        x: 600
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8670404314a7a8846b42c7ef15473789
      internalID: 933244598
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_0: -610628573
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_1: -1103683973
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_2: -609046576
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_3: 666538622
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_4: 1146351855
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_5: -1816087596
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_6: -387730858
      Animation_Icon_Profile_Emoji_007_Kurai_00_SR_101_7: 933244598
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
