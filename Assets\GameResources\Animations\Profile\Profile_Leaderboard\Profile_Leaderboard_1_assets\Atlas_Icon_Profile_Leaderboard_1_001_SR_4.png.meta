fileFormatVersion: 2
guid: 81cedcd5b12a76c4dbff7b965c44390e
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_0
      rect:
        serializedVersion: 2
        x: 0
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 406dfed7237ffbf42adb38628a58a7d8
      internalID: -4226220
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_1
      rect:
        serializedVersion: 2
        x: 200
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 02e7baa892bb76b41b344ce87a6dd019
      internalID: 1726361683
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_2
      rect:
        serializedVersion: 2
        x: 400
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6767e2c264ae82e4e861a189fb727036
      internalID: 315956065
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_3
      rect:
        serializedVersion: 2
        x: 600
        y: 400
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3f7d6b30c6654c44aac921bc48535d49
      internalID: -1402647467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_4
      rect:
        serializedVersion: 2
        x: 0
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9d8da9b2d9101704a8bd74976fb3acaa
      internalID: -551150852
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_5
      rect:
        serializedVersion: 2
        x: 200
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cf4a14dce92a376478069691090c355c
      internalID: -2040014887
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_6
      rect:
        serializedVersion: 2
        x: 400
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eaeb0c067956bf24095d6400727f1133
      internalID: 2109819934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_7
      rect:
        serializedVersion: 2
        x: 600
        y: 200
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1c29156d09de5be44a55c30dc4078124
      internalID: 2096356277
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_8
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: db44c3bd64ae7f94591a0026709a093a
      internalID: -1539822839
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_9
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4083a693c1d30f442ba588b8d8b7ec06
      internalID: 58673407
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_10
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3027e7e48f3862d4786a7d279892d7f2
      internalID: -360582323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Atlas_Icon_Profile_Leaderboard_1_001_SR_4_11
      rect:
        serializedVersion: 2
        x: 600
        y: 0
        width: 200
        height: 200
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ce704207417113458758afc446ca58f
      internalID: -71576629
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_0: -4226220
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_1: 1726361683
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_10: -360582323
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_11: -71576629
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_2: 315956065
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_3: -1402647467
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_4: -551150852
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_5: -2040014887
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_6: 2109819934
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_7: 2096356277
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_8: -1539822839
      Atlas_Icon_Profile_Leaderboard_1_001_SR_4_9: 58673407
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
