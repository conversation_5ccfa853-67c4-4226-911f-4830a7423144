# Addressables Editor/CDN 調整紀錄

摘要
- 目的：在 Unity Editor 環境中模擬 Android 客戶端從 CDN 下載 Addressables 的行為，且避免 Editor 使用本機相對路徑時觸發 UnityWebRequest 的連線錯誤。
- 主要修改檔案：`Assets/Scripts/Utilities/AssetManager/Addressables/AddressableAssetManager.cs`

變更清單（高階）
1. 在 `CheckUpdateAsync()` 補上詳細 debug log（Editor Console 可看到）：
   - 方法入口列印：`HasRemoteContent`、`_remoteHashUrl`、`_cdnSetting.urlFormat`。
   - 下載到 remote hash 後列印 `remoteHash` 與 local/main catalog hash（`_mainCatalogHash`）。
   - 計算需下載的 keys 時列印 `NeedToUpdateKeys.Count`、每個 key 清單與總 byte 與 MB。

2. 改進 `DownloadTextFileAsync(string url)`：
   - 若 `File.Exists(url)` 為真，直接使用 `File.ReadAllText(url)` 讀取並回傳（避免 Editor 對 Library/... 路徑走 UnityWebRequest 而出現 "Cannot connect to destination host"）。
   - 否則 fallback 用 `UnityWebRequest.Get(url)` 來支援 http(s) 或 file://。
   - 保持錯誤紀錄與失敗回傳 (false, null)。

3. 在 `CheckUpdateAsync()` 中加入 Editor 分支（`#if UNITY_EDITOR`）：
   - 若在 Editor 且 `_cdnSetting.urlFormat` 有設定，優先用 `string.Format(_cdnSetting.urlFormat, catalogHashName)` 來組出 `remoteHashPath`，以便在 Editor 模擬從 CDN 取得 `.hash` / `.json`。
   - 非 Editor（實際裝置）仍維持原本邏輯，不受影響。

為什麼這些修改是必要的？
- 原因 A：有些 catalog/hash 在 Addressables 設定中可能指向本機的 `Library/...` 路徑。UnityWebRequest 嘗試連這種沒有 scheme 的路徑會拋出 "Cannot connect to destination host"。
- 原因 B：要在 Editor 中模擬 Android/CDN 下載流程，單純改變 platform 編號不足以讓 Addressables 在 Editor 以遠端 catalog 及 bundle 下載流程執行；必須讓 Editor 使用 CDN 的 URL 組法。加入 Editor 的 `urlFormat` 優先邏輯可以做到這點，且不會改變真實 build 的行為。

如何在另一個分支完整重放這些修改
1. 切換到目標分支：

```powershell
# 在工作目錄 c:\CP\ProjectCP
git checkout <your-target-branch>
```

2. 把本文件（`docs/ADDRESSABLES_EDITOR_CDN_CHANGELOG.md`）一起切過去（或直接在該分支重做修改）：

```powershell
# 若你已經 commit 了本分支的修改，建議把檔案 cherry-pick 或手動 apply patch。
```

3. 若要手動重放修改（步驟） - 直接編輯 `AddressableAssetManager.cs`：
   - 在 `CheckUpdateAsync()` 開頭加入：
     - `Logger.Debug(Label, $"CheckUpdateAsync called. HasRemoteContent: {HasRemoteContent}, RemoteHashUrl: {_remoteHashUrl}, CdnUrlFormat: {_cdnSetting?.urlFormat ?? "(null)"}");`
     - 若 `!HasRemoteContent`，加入一行 debug 並回傳 EmptySuccess。
   - 在計算並下載 remote hash 成功處，加入：
     - `Logger.Debug(Label, $"Downloaded remote hash: {remoteHash}");`
     - `Logger.Debug(Label, $"Local/main catalog hash: {_mainCatalogHash}");`
   - 在計算需要下載的 keys 與大小後，加入：
     - `Logger.Debug(Label, $"NeedToUpdateKeys.Count: {needToUpdateKeys?.Count ?? 0}");`
     - `Logger.Debug(Label, $"DownloadSize: {size} bytes ({size / 1048576.0:F2} MB)");`
     - `Logger.Debug(Label, $"NeedUpdateKeys: {sb}");`（`sb` 是收集 key 的 StringBuilder）
   - 對 `remoteHashPath` 的建立，新增 `#if UNITY_EDITOR` 分支，當 `_cdnSetting.urlFormat` 有值時在 Editor 優先使用 CDN 組法。
   - 修改 `DownloadTextFileAsync(string url)`：
     - 首先判斷 `if (string.IsNullOrEmpty(url)) return (false, null);`
     - 若 `File.Exists(url)` 則 `return (true, File.ReadAllText(url));`（可 await UniTask.Yield() 以避免同步阻塞）
     - 否則使用 `UnityWebRequest.Get(url)` 並 `await www.SendWebRequest()`，成功則回傳 `www.downloadHandler.text`。

4. 編譯與測試
   - 回到 Unity Editor，開啟 Console。執行你的測試流程（通常是在啟動時執行 `ContentUpdateAsync` 或手動呼叫 `AddressableAssetManager.InitializeAsync()` + `CheckUpdateAsync()`）。
   - 期待看到 Console 中的 debug 訊息：
     - `CheckUpdateAsync called. HasRemoteContent: True` 以及 `CdnUrlFormat` 與 `Downloaded remote hash: ...`
     - `NeedToUpdateKeys.Count` 與 `DownloadSize`（bytes / MB）
   - 若出現 `Cannot connect to destination host` 或 `Cannot resolve destination host`，代表目前遠端 bundle URL（例如日誌中顯示的 `http://remoteurl/...`）不是有效可解析的 host，需檢查 CDN `urlFormat` 與實際可公開存取的資源位置。

Debug 與排錯要點
- 若 `Downloaded remote hash` 有值但 `Local/main catalog hash` 為空：代表本地沒快取該 catalog（新用戶情境），於是 `NeedToUpdateKeys.Count` 會顯示所有需下載的 bundle，接著呼叫 `DownloadUpdateAsync()` 才會真正下載。
- 若在下載 bundle 時出現 `Cannot resolve destination host`：檢查由 locator 轉換出來的 bundle URL（由 `TransformInternalId` 決定）。確保 `_cdnSetting.urlFormat` 對應的 host 可以被解析並可存取（例如有正確 domain 與權限）。
- 在 Editor 測試時務必確保 `SetCdnSetting(...)` 已被呼叫並填入 `urlFormat` 與 `authorization`（若需要）。

建議下一步
- 我可以依此文件在另一分支自動套用相同修改（如果你切換到那個分支並回來通知我，我可以直接在該分支上執行相同步驟的檔案修改）。
- 如果你要我把測試流程做成 Editor 工具按鈕（按下會執行 CheckUpdateAsync 並顯示結果），我可以在 `Assets/Editor/` 新增一個簡易窗口並實作。

---
文件建立時間: 2025-08-26
由: 專案自動變更紀錄

## 關於下載併發（結論）

- 目前實作要點：`AddressableAssetManager.cs` 在 `CheckUpdateAsync()` 會計算出需下載的 keys，實際下載在 `DownloadUpdateAsync()` 透過
  `Addressables.DownloadDependenciesAsync(updateResult.needToUpdateKeys, ...)` 觸發。程式碼未在呼叫端對併發數做限制或以批次方式分割下載。
- 專案內大量使用 `UniTask.WhenAll(...)`，會同時啟動所有任務，等同沒有上限的併發控制（併發量取決於任務數與 Addressables/runtime 內部行為）。
- 專案有一個 `AsyncLock`（以 `SemaphoreSlim(1,1)` 實作）用於保護臨界區，但搜尋結果顯示它並未用於下載節流；亦未找到類似 `SemaphoreSlim` 或 maxConcurrent 直接控制下載併發的實作。

結論：目前下載併發由 Unity/Addressables runtime 與底層 HTTP stack 決定，專案程式層沒有明確的併發上限。

建議選項（由簡到複）：
1. 最小改動（建議先試）：在 `DownloadUpdateAsync` 將 `needToUpdateKeys` 切成固定大小的批次（例如 batchSize = 8 或 16），依序呼叫 `Addressables.DownloadDependenciesAsync`。
  - 優點：實作簡單、風險低、可立即控制同時下載的 bundle 數量。
2. 中階（較多改動）：自行取得每個 bundle 的 URL，使用 `UnityWebRequest` 做下載，並以 `SemaphoreSlim`（或 `AsyncLock` + 計數）限制最大併發。
  - 優點：可精細控制重試、超時、授權 header、寫入 cache 時機。缺點：需處理 Addressables cache 與資源一致性，實作較複雜。
3. 環境面：檢查 Addressables / ResourceManager 設定或 CDN/伺服器端的連線限制，如果能從伺服器端節流或優化，可能更穩定且不需在客戶端大改。

下一步建議：若要我實作，建議先從第 1 項（批次呼叫）開始；目前你要求先不要實作，等你確認要採取哪一選項再進行修改。
