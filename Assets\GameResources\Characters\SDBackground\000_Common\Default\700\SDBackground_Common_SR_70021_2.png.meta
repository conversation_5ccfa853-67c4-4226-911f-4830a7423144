fileFormatVersion: 2
guid: 4c850d366719fdd4386dac9886eb395a
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: AmbientBlueLight
      rect:
        serializedVersion: 2
        x: 638
        y: 3360
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c634eadedf3b3fa429b76505c48f98e5
      internalID: 1999024351
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: AmbientGreenLight
      rect:
        serializedVersion: 2
        x: 1
        y: 3334
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 835a120bd290ead42b322d10dffd2ace
      internalID: -704717286
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: BlackCat_1
      rect:
        serializedVersion: 2
        x: 1817
        y: 3354
        width: 129
        height: 169
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f9d181ab7c82f2f4385ad7ee0c054f13
      internalID: 701815899
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: BlackCat_2
      rect:
        serializedVersion: 2
        x: 1670
        y: 3346
        width: 129
        height: 169
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 37fede8b3013918489e66b078d039813
      internalID: 965402100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: BlackCat_3
      rect:
        serializedVersion: 2
        x: 1801
        y: 3183
        width: 129
        height: 169
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: de99db0a93513474abff338b9cea0400
      internalID: 333406731
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Build
      rect:
        serializedVersion: 2
        x: 1154
        y: 3360
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca19dc2eabde6474fa17564ba257529f
      internalID: -346129465
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Ground
      rect:
        serializedVersion: 2
        x: 517
        y: 2623
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 76b14f6b084f4c74496267d0b3a328ad
      internalID: 978587988
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Light
      rect:
        serializedVersion: 2
        x: 1549
        y: 3328
        width: 121
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: df335395079e66640b5a5f509df18f4f
      internalID: -314642760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RadLight
      rect:
        serializedVersion: 2
        x: 1670
        y: 3795
        width: 338
        height: 274
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7f58c79eb3972b429d7814a6fcb5a17
      internalID: 2021402211
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Star02
      rect:
        serializedVersion: 2
        x: 1549
        y: 3078
        width: 410
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3c446562cd3abc4eb762ec34cb534c2
      internalID: -1956144833
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TrainWindow
      rect:
        serializedVersion: 2
        x: 1
        y: 4071
        width: 635
        height: 24
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 00aea4d38d13f5946b25b26bbdef6542
      internalID: -785965779
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: WhiteCat_1
      rect:
        serializedVersion: 2
        x: 1670
        y: 3656
        width: 145
        height: 137
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7cef54ef4637dba4db4d6af47538c73e
      internalID: 965730337
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: WhiteCat_2
      rect:
        serializedVersion: 2
        x: 1817
        y: 3656
        width: 145
        height: 137
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8877298e43baf2c41ad200b0ab6b0eb7
      internalID: -432245229
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: WhiteCat_3
      rect:
        serializedVersion: 2
        x: 1670
        y: 3517
        width: 145
        height: 137
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 44a73707c65520c42beb9617e57bcdbd
      internalID: 163932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: WhiteCat_4
      rect:
        serializedVersion: 2
        x: 1817
        y: 3525
        width: 145
        height: 129
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 297472baeaaf4844a804660bd1833a74
      internalID: -110510725
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Railing
      rect:
        serializedVersion: 2
        x: 1
        y: 2597
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 06c896a931f462247adfd24a4e2db90d
      internalID: 1505499476
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Sign_1
      rect:
        serializedVersion: 2
        x: 1033
        y: 2623
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 91be5987c7e0be14db8eb43ee8bebbf8
      internalID: -1906281738
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Sign_2
      rect:
        serializedVersion: 2
        x: 517
        y: 1886
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b505f16202b6e1a43867bee6eb7212cb
      internalID: 128088270
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Sign_3
      rect:
        serializedVersion: 2
        x: 1
        y: 1860
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8aeffa499ba82f64d800a4fc71aa9adb
      internalID: 1377105575
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Sign_4
      rect:
        serializedVersion: 2
        x: 1033
        y: 1886
        width: 514
        height: 735
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 76556b6c91d4012408c9a54d538bd549
      internalID: 1486434375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Railroad Tracks
      rect:
        serializedVersion: 2
        x: 1670
        y: 4071
        width: 361
        height: 24
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb5676f7c4067b846ab850063c24a4e5
      internalID: -465011394
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 241038550ae83ac46a68a95b56ef7691
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      AmbientBlueLight: 1999024351
      AmbientGreenLight: -704717286
      BlackCat_1: 701815899
      BlackCat_2: 965402100
      BlackCat_3: 333406731
      Build: -346129465
      Ground: 978587988
      Light: -314642760
      RadLight: 2021402211
      Railing: 1505499476
      Railroad Tracks: -465011394
      Sign_1: -1906281738
      Sign_2: 128088270
      Sign_3: 1377105575
      Sign_4: 1486434375
      Star02: -1956144833
      TrainWindow: -785965779
      WhiteCat_1: 965730337
      WhiteCat_2: -432245229
      WhiteCat_3: 163932
      WhiteCat_4: -110510725
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
