%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Animation_Icon_Profile_Common_SR_70031
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: -316923893, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.13333334
      value: {fileID: -158485120, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.25
      value: {fileID: 1340542930, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.38333333
      value: {fileID: -208885097, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.51666665
      value: {fileID: -1556887477, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.6166667
      value: {fileID: -684212014, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.75
      value: {fileID: 1853051891, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 0.8666667
      value: {fileID: -1001672890, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - time: 1
      value: {fileID: -316923893, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    attribute: m_Sprite
    path: 
    classID: 114
    script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2015549526
      script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: -316923893, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: -158485120, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: 1340542930, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: -208885097, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: -1556887477, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: -684212014, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: 1853051891, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: -1001672890, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
    - {fileID: -316923893, guid: 8c2afebc3250fd24e841d18c8e18e01f, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 1.0166667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events:
  - time: 0
    functionName: 
    data: 
    objectReferenceParameter: {fileID: 0}
    floatParameter: 0
    intParameter: 0
    messageOptions: 0
  - time: 0
    functionName: 
    data: 
    objectReferenceParameter: {fileID: 0}
    floatParameter: 0
    intParameter: 0
    messageOptions: 0
