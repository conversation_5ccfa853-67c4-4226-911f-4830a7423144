fileFormatVersion: 2
guid: 1c2668d0deabf624286a33e836b17373
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 3
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 3
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: SDBackground_Common_100_2_101
      rect:
        serializedVersion: 2
        x: 371
        y: 1788
        width: 120
        height: 257
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0af1899b028b5c14f873a409b7dd0944
      internalID: 1883709624
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_101Light01
      rect:
        serializedVersion: 2
        x: 3
        y: 2021
        width: 80
        height: 24
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b38fc6ab5f2a3cd42aec80aee08cb92e
      internalID: -1240409810
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_101Light02
      rect:
        serializedVersion: 2
        x: 135
        y: 1949
        width: 160
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 55d07fb451a75384c9e9d5142af9b924
      internalID: -1443559446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_101Light03
      rect:
        serializedVersion: 2
        x: 891
        y: 1805
        width: 200
        height: 240
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ef151d7aaa4562c438075a8e1ccd5616
      internalID: 1093454482
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_Building
      rect:
        serializedVersion: 2
        x: 3
        y: 810
        width: 514
        height: 520
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2b01f31ff367b954cbc7a69b23631f9a
      internalID: -1093358437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_BuildingLight01
      rect:
        serializedVersion: 2
        x: 89
        y: 1973
        width: 40
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 637cf964b0fd4ad4297be4f67b7b73f8
      internalID: 1257949168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_BuildingLight02
      rect:
        serializedVersion: 2
        x: 523
        y: 690
        width: 514
        height: 640
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 41e875738f3839d438f446ad79f156ab
      internalID: 1649489428
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_BuildingLight03
      rect:
        serializedVersion: 2
        x: 3
        y: 1527
        width: 514
        height: 216
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b8052e46a7942744bac08c65bcebb42
      internalID: 1370885292
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_BuleSign
      rect:
        serializedVersion: 2
        x: 615
        y: 1902
        width: 270
        height: 143
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dee5f689ef6cc2a488d2f1c100ad8210
      internalID: 337110183
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_City
      rect:
        serializedVersion: 2
        x: 1399
        y: 1336
        width: 514
        height: 407
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f67d1a2e499188e47aef4338a44aa485
      internalID: 1560555062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_CityBlueLight
      rect:
        serializedVersion: 2
        x: 879
        y: 1392
        width: 514
        height: 351
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6479aab1563c46c46947ac5d3f400783
      internalID: 458126038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_FanimaxSign
      rect:
        serializedVersion: 2
        x: 301
        y: 1797
        width: 64
        height: 248
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8b2f4fba7c92e384793caffecd04e11c
      internalID: -1742493208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_FanimaxSignLight
      rect:
        serializedVersion: 2
        x: 497
        y: 1749
        width: 112
        height: 296
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb0f2d70e13f97940a9d40fc22b6eebc
      internalID: -549547701
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_Floor
      rect:
        serializedVersion: 2
        x: 1097
        y: 1932
        width: 514
        height: 113
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2c9d64d2a86ff654796dea1ab2af97ed
      internalID: -135502736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: SDBackground_Common_100_2_TaipeiSign
      rect:
        serializedVersion: 2
        x: 523
        y: 1423
        width: 350
        height: 320
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 17cf29e699df6b04b8641501e6d17745
      internalID: -599898435
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      SDBackground_Common_100_2_101: 1883709624
      SDBackground_Common_100_2_101Light01: -1240409810
      SDBackground_Common_100_2_101Light02: -1443559446
      SDBackground_Common_100_2_101Light03: 1093454482
      SDBackground_Common_100_2_Building: -1093358437
      SDBackground_Common_100_2_BuildingLight01: 1257949168
      SDBackground_Common_100_2_BuildingLight02: 1649489428
      SDBackground_Common_100_2_BuildingLight03: 1370885292
      SDBackground_Common_100_2_BuleSign: 337110183
      SDBackground_Common_100_2_City: 1560555062
      SDBackground_Common_100_2_CityBlueLight: 458126038
      SDBackground_Common_100_2_FanimaxSign: -1742493208
      SDBackground_Common_100_2_FanimaxSignLight: -549547701
      SDBackground_Common_100_2_Floor: -135502736
      SDBackground_Common_100_2_TaipeiSign: -599898435
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
